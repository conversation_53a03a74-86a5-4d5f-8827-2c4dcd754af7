"""Perplexity-powered Product Description Enhancement Tool."""

import json
import logging
from typing import Dict, Any, List, Optional
from crewai_tools import BaseTool
from pydantic import BaseModel, Field
import requests
import os

logger = logging.getLogger(__name__)


class ProductDescriptionInput(BaseModel):
    """Input schema for product description enhancement."""
    product_name: str = Field(..., description="Product name to enhance description for")
    current_description: Optional[str] = Field(None, description="Current product description (if any)")
    category: str = Field(..., description="Product category (e.g., 'fruit', 'electronics')")
    vendor: str = Field(..., description="Vendor name (e.g., 'asda', 'tesco')")
    price: Optional[str] = Field(None, description="Product price for context")
    weight: Optional[str] = Field(None, description="Product weight/size for context")


class PerplexityDescriptionTool(BaseTool):
    """Perplexity-powered tool for generating comprehensive product descriptions."""

    name: str = "perplexity_description_tool"
    description: str = """
    AI-powered product description enhancement tool using Perplexity AI.
    
    Features:
    - Generate comprehensive, marketing-quality product descriptions
    - Research product benefits, uses, and characteristics
    - Create engaging, informative descriptions from basic product names
    - Maintain consistency with vendor tone and category standards
    - Include nutritional info, usage tips, and key features where relevant
    
    Use this tool to transform basic product names into rich, detailed descriptions
    that provide value to customers and improve product presentation.
    """
    args_schema = ProductDescriptionInput

    def __init__(self):
        """Initialize the Perplexity description tool."""
        super().__init__()
        self._logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        
        # Get API key from environment or config
        self.api_key = os.getenv("PERPLEXITY_API_KEY")
        if not self.api_key:
            self._logger.warning("PERPLEXITY_API_KEY not found in environment variables")
        
        self.base_url = "https://api.perplexity.ai/chat/completions"

    def _run(
        self,
        product_name: str,
        category: str,
        vendor: str,
        current_description: Optional[str] = None,
        price: Optional[str] = None,
        weight: Optional[str] = None
    ) -> str:
        """
        Generate enhanced product description using Perplexity AI.
        
        Args:
            product_name: Name of the product
            category: Product category
            vendor: Vendor name
            current_description: Existing description (optional)
            price: Product price (optional)
            weight: Product weight/size (optional)
            
        Returns:
            Enhanced product description as JSON string
        """
        try:
            self._logger.info(f"[PERPLEXITY] Enhancing description for: {product_name}")
            
            if not self.api_key:
                return self._fallback_description(product_name, current_description)
            
            # Build context-aware prompt
            prompt = self._build_enhancement_prompt(
                product_name, category, vendor, current_description, price, weight
            )
            
            # Call Perplexity API
            enhanced_description = self._call_perplexity_api(prompt)
            
            # Format result
            result = {
                "original_name": product_name,
                "enhanced_description": enhanced_description,
                "category": category,
                "vendor": vendor,
                "enhancement_source": "perplexity_ai"
            }
            
            self._logger.info(f"[PERPLEXITY] Description enhanced successfully")
            return json.dumps(result, indent=2)
            
        except Exception as e:
            self._logger.error(f"[PERPLEXITY] Enhancement failed: {e}")
            return self._fallback_description(product_name, current_description)

    def _build_enhancement_prompt(
        self,
        product_name: str,
        category: str,
        vendor: str,
        current_description: Optional[str],
        price: Optional[str],
        weight: Optional[str]
    ) -> str:
        """Build context-aware prompt for description enhancement."""
        
        # Base prompt structure
        prompt = f"""Generate a comprehensive, engaging product description for "{product_name}" from {vendor.upper()}.

Product Context:
- Category: {category}
- Vendor: {vendor.upper()}"""
        
        if price:
            prompt += f"\n- Price: {price}"
        if weight:
            prompt += f"\n- Size/Weight: {weight}"
        if current_description:
            prompt += f"\n- Current Description: {current_description}"

        prompt += f"""

Requirements:
1. Create a detailed, marketing-quality description (2-3 sentences)
2. Include key benefits, uses, or characteristics relevant to {category}
3. Maintain {vendor.upper()}'s tone (friendly, informative, value-focused)
4. Add relevant details like nutritional benefits, usage tips, or key features
5. Make it engaging and informative for online shoppers
6. Keep it concise but comprehensive

Focus on what makes this product appealing and useful to customers.
Return ONLY the enhanced description text, no additional formatting or explanations."""

        return prompt

    def _call_perplexity_api(self, prompt: str) -> str:
        """Call Perplexity API for description enhancement."""
        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            payload = {
                "model": "sonar-pro",
                "messages": [
                    {
                        "role": "system",
                        "content": "You are an expert product description writer specializing in ecommerce content. Create engaging, informative descriptions that help customers understand product value."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                "max_tokens": 300,
                "temperature": 0.3,
                "top_p": 0.9
            }
            
            response = requests.post(self.base_url, json=payload, headers=headers, timeout=30)
            response.raise_for_status()
            
            result = response.json()
            enhanced_description = result["choices"][0]["message"]["content"].strip()
            
            return enhanced_description
            
        except Exception as e:
            self._logger.error(f"Perplexity API call failed: {e}")
            raise

    def _fallback_description(self, product_name: str, current_description: Optional[str]) -> str:
        """Provide fallback description when Perplexity is unavailable."""
        fallback_desc = current_description or f"High-quality {product_name.lower()} available for purchase."
        
        result = {
            "original_name": product_name,
            "enhanced_description": fallback_desc,
            "enhancement_source": "fallback",
            "note": "Perplexity API unavailable - using fallback description"
        }
        
        return json.dumps(result, indent=2)

    def _sanitize_product_name(self, product_name: str) -> str:
        """Clean and sanitize product name for better API results."""
        # Remove common prefixes/suffixes that don't add value
        prefixes_to_remove = ["ASDA", "TESCO", "JUST ESSENTIALS by"]
        
        cleaned_name = product_name
        for prefix in prefixes_to_remove:
            if cleaned_name.startswith(prefix):
                cleaned_name = cleaned_name[len(prefix):].strip()
        
        return cleaned_name
